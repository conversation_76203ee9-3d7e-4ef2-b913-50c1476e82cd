import { useEffect, useState } from "react"
import { useSearchParams, useNavigate } from "react-router-dom"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import RepositorySettings from "@/components/settings/RepositorySettings"
import UsageTrackingSettings from "@/components/settings/UsageTrackingSettings"
import ScanLogsPage from "@/pages/Settings/ScanLogsPage"

const Settings = () => {
  const [searchParams, setSearchParams] = useSearchParams()
  const navigate = useNavigate()
  const [activeTab, setActiveTab] = useState("general")

  // Get tab from URL parameter
  useEffect(() => {
    const tab = searchParams.get("tab")
    if (tab && ["general", "repository", "usage-tracking", "scan-logs"].includes(tab)) {
      setActiveTab(tab)
    }
  }, [searchParams])

  // Update URL when tab changes
  const handleTabChange = (value: string) => {
    setActiveTab(value)
    setSearchParams({ tab: value })
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Settings</h1>
      </div>

      <Tabs value={activeTab} onValueChange={handleTabChange}>
        <TabsList className="mb-4">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="repository">Repository Integration</TabsTrigger>
          <TabsTrigger value="usage-tracking">Usage Tracking</TabsTrigger>
          <TabsTrigger value="scan-logs">Scan Logs</TabsTrigger>
        </TabsList>

        <TabsContent value="general">
          <Card>
            <CardHeader>
              <CardTitle>General Settings</CardTitle>
              <CardDescription>
                Configure general application settings.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="appName">Application Name</Label>
                  <Input id="appName" defaultValue="ADGitOps UI" />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="dataDirectory">Data Directory</Label>
                  <Input id="dataDirectory" defaultValue="./data" />
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button>Save Changes</Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="repository">
          <RepositorySettings />
        </TabsContent>

        <TabsContent value="usage-tracking">
          <UsageTrackingSettings />
        </TabsContent>

        <TabsContent value="scan-logs">
          <ScanLogsPage />
        </TabsContent>

        {/* Export settings tab removed as it's now covered by the Report feature */}
      </Tabs>
    </div>
  )
}

export default Settings
